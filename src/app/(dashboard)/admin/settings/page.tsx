"use client"

import {useSettings} from "@/contexts/settings-context"
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger} from "@/components/ui/tabs"
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from "@/components/ui/card"
import {Input} from "@/components/ui/input"
import {Label} from "@/components/ui/label"
import {Switch} from "@/components/ui/switch"
import {Button} from "@/components/ui/button"
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from "@/components/ui/select"
import {Slider} from "@/components/ui/slider"
import {Checkbox} from "@/components/ui/checkbox"
import {RadioGroup, RadioGroupItem} from "@/components/ui/radio-group"
import {Avatar, AvatarFallback, AvatarImage} from "@/components/ui/avatar"
import {Laptop, Smartphone, Tablet} from "lucide-react"
import {useState} from "react"
import {toast} from "sonner"

const defaultAvatars = [
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9439775.jpg-4JVJWOjPksd3DtnBYJXoWHA5lc1DU9.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/375238645_11475210.jpg-lU8bOe6TLt5Rv51hgjg8NT8PsDBmvN.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/375238208_11475222.jpg-poEIzVHAGiIfMFQ7EiF8PUG1u0Zkzz.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/dd.jpg-4MCwPC2Bec6Ume26Yo1kao3CnONxDg.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9334178.jpg-Y74tW6XFO68g7N36SE5MSNDNVKLQ08.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/5295.jpg-fLw0wGGZp8wuTzU5dnyfjZDwAHN98a.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9720029.jpg-Yf9h2a3kT7rYyCb648iLIeHThq5wEy.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/27470341_7294795.jpg-XE0zf7R8tk4rfA1vm4fAHeZ1QoVEOo.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/799.jpg-0tEi4Xvg5YsFoGoQfQc698q4Dygl1S.jpeg",
    "https://hebbkx1anhila5yf.public.blob.vercel-storage.com/9334228.jpg-eOsHCkvVrVAwcPHKYSs5sQwVKsqWpC.jpeg",
]

export default function SettingsPage() {
    const {settings, updateSettings, updateNotificationSettings, updatePrivacySettings} = useSettings()
    const [selectedAvatar, setSelectedAvatar] = useState(settings.avatar)

    const handleSaveAccount = () => {
        updateSettings({
            avatar: selectedAvatar,
            fullName: settings.fullName,
            email: settings.email,
            phone: settings.phone,
            timezone: settings.timezone,
        })
        toast.success("Account settings saved successfully")
    }

    const handleSaveNotifications = () => {
        updateNotificationSettings(settings.notifications)
        toast.success("Notification settings saved successfully")
    }

    const handleSavePrivacy = () => {
        updatePrivacySettings(settings.privacy)
        toast.success("Privacy settings saved successfully")
    }

    return (
        <div className="container mx-auto py-10">
            <h1 className="text-3xl font-bold mb-6">Settings</h1>
            <Tabs defaultValue="account" className="space-y-4">
                <TabsList className="grid w-full grid-cols-5">
                    <TabsTrigger value="account">Account</TabsTrigger>
                    <TabsTrigger value="security">Security</TabsTrigger>
                    <TabsTrigger value="preferences">Preferences</TabsTrigger>
                    <TabsTrigger value="notifications">Notifications</TabsTrigger>
                    <TabsTrigger value="privacy">Privacy</TabsTrigger>
                </TabsList>

                <TabsContent value="account">
                    <Card>
                        <CardHeader>
                            <CardTitle>Account Settings</CardTitle>
                            <CardDescription>Manage your account information</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="space-y-4">
                                <Label>Current Avatar</Label>
                                <div className="flex items-center space-x-4">
                                    <Avatar className="h-20 w-20">
                                        <AvatarImage src={selectedAvatar} alt={settings.fullName}/>
                                        <AvatarFallback>
                                            {settings.fullName
                                                .split(" ")
                                                .map((n) => n[0])
                                                .join("")}
                                        </AvatarFallback>
                                    </Avatar>
                                </div>
                                <Label>Choose a new avatar</Label>
                                <div className="flex gap-4 overflow-x-auto pb-2">
                                    {defaultAvatars.map((avatar, index) => (
                                        <Avatar
                                            key={index}
                                            className={`h-20 w-20 rounded-lg cursor-pointer hover:ring-2 hover:ring-primary shrink-0 ${
                                                selectedAvatar === avatar ? "ring-2 ring-primary" : ""
                                            }`}
                                            onClick={() => setSelectedAvatar(avatar)}
                                        >
                                            <AvatarImage src={avatar} alt={`Avatar ${index + 1}`}
                                                         className="object-cover"/>
                                            <AvatarFallback>{index + 1}</AvatarFallback>
                                        </Avatar>
                                    ))}
                                </div>
                                <div>
                                    <Label htmlFor="custom-avatar">Or upload a custom avatar</Label>
                                    <Input id="custom-avatar" type="file" accept="image/*" className="mt-1"/>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="full-name">Full Name</Label>
                                <Input
                                    id="full-name"
                                    value={settings.fullName}
                                    onChange={(e) => updateSettings({fullName: e.target.value})}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    value={settings.email}
                                    onChange={(e) => updateSettings({email: e.target.value})}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone Number</Label>
                                <Input
                                    id="phone"
                                    type="tel"
                                    value={settings.phone}
                                    onChange={(e) => updateSettings({phone: e.target.value})}
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="timezone">Timezone</Label>
                                <Select value={settings.timezone}
                                        onValueChange={(value) => updateSettings({timezone: value})}>
                                    <SelectTrigger id="timezone">
                                        <SelectValue placeholder="Select Timezone"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="utc-12">International Date Line West (UTC-12)</SelectItem>
                                        <SelectItem value="utc-11">Samoa Standard Time (UTC-11)</SelectItem>
                                        <SelectItem value="utc-10">Hawaii-Aleutian Standard Time (UTC-10)</SelectItem>
                                        <SelectItem value="utc-9">Alaska Standard Time (UTC-9)</SelectItem>
                                        <SelectItem value="utc-8">Pacific Time (UTC-8)</SelectItem>
                                        <SelectItem value="utc-7">Mountain Time (UTC-7)</SelectItem>
                                        <SelectItem value="utc-6">Central Time (UTC-6)</SelectItem>
                                        <SelectItem value="utc-5">Eastern Time (UTC-5)</SelectItem>
                                        <SelectItem value="utc-4">Atlantic Time (UTC-4)</SelectItem>
                                        <SelectItem value="utc-3">Argentina Standard Time (UTC-3)</SelectItem>
                                        <SelectItem value="utc-2">South Georgia Time (UTC-2)</SelectItem>
                                        <SelectItem value="utc-1">Azores Time (UTC-1)</SelectItem>
                                        <SelectItem value="utc+0">Greenwich Mean Time (UTC+0)</SelectItem>
                                        <SelectItem value="utc+1">Central European Time (UTC+1)</SelectItem>
                                        <SelectItem value="utc+2">Eastern European Time (UTC+2)</SelectItem>
                                        <SelectItem value="utc+3">Moscow Time (UTC+3)</SelectItem>
                                        <SelectItem value="utc+4">Gulf Standard Time (UTC+4)</SelectItem>
                                        <SelectItem value="utc+5">Pakistan Standard Time (UTC+5)</SelectItem>
                                        <SelectItem value="utc+5.5">Indian Standard Time (UTC+5:30)</SelectItem>
                                        <SelectItem value="utc+6">Bangladesh Standard Time (UTC+6)</SelectItem>
                                        <SelectItem value="utc+7">Indochina Time (UTC+7)</SelectItem>
                                        <SelectItem value="utc+8">China Standard Time (UTC+8)</SelectItem>
                                        <SelectItem value="utc+9">Japan Standard Time (UTC+9)</SelectItem>
                                        <SelectItem value="utc+10">Australian Eastern Standard Time
                                            (UTC+10)</SelectItem>
                                        <SelectItem value="utc+11">Solomon Islands Time (UTC+11)</SelectItem>
                                        <SelectItem value="utc+12">New Zealand Standard Time (UTC+12)</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button onClick={handleSaveAccount}>Save Account Settings</Button>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="security">
                    <div className="grid gap-4 md:grid-cols-2">
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Security Settings</CardTitle>
                                <CardDescription>Manage your account&#39;s security settings</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="current-password">Current Password</Label>
                                    <Input id="current-password" type="password"/>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="new-password">New Password</Label>
                                    <Input id="new-password" type="password"/>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                                    <Input id="confirm-password" type="password"/>
                                </div>
                                <div className="flex items-center space-x-2">
                                    <Switch id="two-factor"/>
                                    <Label htmlFor="two-factor">Enable Two-Factor Authentication</Label>
                                </div>
                            </CardContent>
                            <CardFooter>
                                <Button>Save Security Settings</Button>
                            </CardFooter>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Login History</CardTitle>
                                <CardDescription>Recent login activities on your account</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {[
                                    {
                                        date: "2023-07-20",
                                        time: "14:30 UTC",
                                        ip: "***********",
                                        location: "New York, USA"
                                    },
                                    {date: "2023-07-19", time: "09:15 UTC", ip: "********", location: "London, UK"},
                                    {date: "2023-07-18", time: "22:45 UTC", ip: "**********", location: "Tokyo, Japan"},
                                ].map((login, index) => (
                                    <div key={index} className="flex justify-between items-center text-sm">
                    <span>
                      {login.date} {login.time}
                    </span>
                                        <span>{login.ip}</span>
                                        <span>{login.location}</span>
                                    </div>
                                ))}
                            </CardContent>
                        </Card>

                        <Card>
                            <CardHeader>
                                <CardTitle>Active Sessions</CardTitle>
                                <CardDescription>Currently active sessions on your account</CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                {[
                                    {device: "Laptop", browser: "Chrome", os: "Windows 10", icon: Laptop},
                                    {device: "Smartphone", browser: "Safari", os: "iOS 15", icon: Smartphone},
                                    {device: "Tablet", browser: "Firefox", os: "Android 12", icon: Tablet},
                                ].map((session, index) => (
                                    <div key={index} className="flex items-center justify-between text-sm">
                    <span className="flex items-center">
                      <session.icon className="mr-2 h-4 w-4"/>
                        {session.device}
                    </span>
                                        <span>{session.browser}</span>
                                        <span>{session.os}</span>
                                    </div>
                                ))}
                            </CardContent>
                            <CardFooter>
                                <Button variant="outline">Log Out All Other Sessions</Button>
                            </CardFooter>
                        </Card>
                    </div>
                </TabsContent>

                <TabsContent value="preferences">
                    <Card>
                        <CardHeader>
                            <CardTitle>Preferences</CardTitle>
                            <CardDescription>Customize your dashboard experience</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label htmlFor="language">Language</Label>
                                    <Select defaultValue="en">
                                        <SelectTrigger id="language">
                                            <SelectValue placeholder="Select Language"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="en">English</SelectItem>
                                            <SelectItem value="es">Español</SelectItem>
                                            <SelectItem value="fr">Français</SelectItem>
                                            <SelectItem value="de">Deutsch</SelectItem>
                                            <SelectItem value="zh">中文</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="currency">Currency</Label>
                                    <Select defaultValue="usd">
                                        <SelectTrigger id="currency">
                                            <SelectValue placeholder="Select Currency"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="usd">USD ($)</SelectItem>
                                            <SelectItem value="eur">EUR (€)</SelectItem>
                                            <SelectItem value="gbp">GBP (£)</SelectItem>
                                            <SelectItem value="jpy">JPY (¥)</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="date-format">Date Format</Label>
                                    <Select defaultValue="mm-dd-yyyy">
                                        <SelectTrigger id="date-format">
                                            <SelectValue placeholder="Select Date Format"/>
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="mm-dd-yyyy">MM-DD-YYYY</SelectItem>
                                            <SelectItem value="dd-mm-yyyy">DD-MM-YYYY</SelectItem>
                                            <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                                        </SelectContent>
                                    </Select>
                                </div>
                                <div className="space-y-2">
                                    <Label htmlFor="font-size">Font Size</Label>
                                    <Slider defaultValue={[16]} max={24} min={12} step={1}/>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label>Theme</Label>
                                <RadioGroup defaultValue="system">
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="light" id="theme-light"/>
                                        <Label htmlFor="theme-light">Light</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="dark" id="theme-dark"/>
                                        <Label htmlFor="theme-dark">Dark</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="system" id="theme-system"/>
                                        <Label htmlFor="theme-system">System</Label>
                                    </div>
                                </RadioGroup>
                            </div>
                            <div className="space-y-2">
                                <Label>Dashboard Layout</Label>
                                <RadioGroup defaultValue="default">
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="default" id="layout-default"/>
                                        <Label htmlFor="layout-default">Default</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="compact" id="layout-compact"/>
                                        <Label htmlFor="layout-compact">Compact</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <RadioGroupItem value="expanded" id="layout-expanded"/>
                                        <Label htmlFor="layout-expanded">Expanded</Label>
                                    </div>
                                </RadioGroup>
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button>Save Preferences</Button>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="notifications">
                    <Card>
                        <CardHeader>
                            <CardTitle>Notification Settings</CardTitle>
                            <CardDescription>Manage how you receive notifications</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid gap-4 md:grid-cols-2">
                                <div className="space-y-2">
                                    <Label>Notification Channels</Label>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="email-notifications"
                                            defaultChecked={settings.notifications.email}
                                            onChange={(e: any) =>
                                                updateNotificationSettings({
                                                    ...settings.notifications,
                                                    email: e.target.checked
                                                })
                                            }
                                        />
                                        <Label htmlFor="email-notifications">Email Notifications</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="push-notifications"
                                            defaultChecked={settings.notifications.push}
                                            onChange={(e: any) =>
                                                updateNotificationSettings({
                                                    ...settings.notifications,
                                                    push: e.target.checked
                                                })
                                            }
                                        />
                                        <Label htmlFor="push-notifications">Push Notifications</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="sms-notifications"
                                            defaultChecked={settings.notifications.sms}
                                            onChange={(e: any) => updateNotificationSettings({
                                                ...settings.notifications,
                                                sms: e.target.checked
                                            })}
                                        />
                                        <Label htmlFor="sms-notifications">SMS Notifications</Label>
                                    </div>
                                </div>
                                <div className="space-y-2">
                                    <Label>Notification Types</Label>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="account-activity"
                                            defaultChecked={settings.notifications.accountActivity}
                                            onChange={(e: any) =>
                                                updateNotificationSettings({
                                                    ...settings.notifications,
                                                    accountActivity: e.target.checked
                                                })
                                            }
                                        />
                                        <Label htmlFor="account-activity">Account Activity</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="new-features"
                                            defaultChecked={settings.notifications.newFeatures}
                                            onChange={(e: any) =>
                                                updateNotificationSettings({
                                                    ...settings.notifications,
                                                    newFeatures: e.target.checked
                                                })
                                            }
                                        />
                                        <Label htmlFor="new-features">New Features and Updates</Label>
                                    </div>
                                    <div className="flex items-center space-x-2">
                                        <Checkbox
                                            id="marketing"
                                            defaultChecked={settings.notifications.marketing}
                                            onChange={(e: any) =>
                                                updateNotificationSettings({
                                                    ...settings.notifications,
                                                    marketing: e.target.checked
                                                })
                                            }
                                        />
                                        <Label htmlFor="marketing">Marketing and Promotions</Label>
                                    </div>
                                </div>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="notification-frequency">Notification Frequency</Label>
                                <Select
                                    value={settings.notifications.frequency}
                                    onValueChange={(value) => updateNotificationSettings({
                                        ...settings.notifications,
                                        frequency: value as "real-time" | "daily" | "weekly"
                                    })}
                                >
                                    <SelectTrigger id="notification-frequency">
                                        <SelectValue placeholder="Select Frequency"/>
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="real-time">Real-time</SelectItem>
                                        <SelectItem value="daily">Daily Digest</SelectItem>
                                        <SelectItem value="weekly">Weekly Summary</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="quiet-hours-start">Quiet Hours</Label>
                                <div className="flex items-center space-x-2">
                                    <Input id="quiet-hours-start" type="time" defaultValue="22:00"/>
                                    <span>to</span>
                                    <Input id="quiet-hours-end" type="time" defaultValue="07:00"/>
                                </div>
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button onClick={handleSaveNotifications}>Save Notification Settings</Button>
                        </CardFooter>
                    </Card>
                </TabsContent>

                <TabsContent value="privacy">
                    <Card>
                        <CardHeader>
                            <CardTitle>Privacy Settings</CardTitle>
                            <CardDescription>Manage your privacy and data settings</CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-6">
                            <div className="grid gap-4 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Data Sharing</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-2">
                                        <div className="flex items-center justify-between">
                                            <Label htmlFor="analytics-sharing">Share analytics data</Label>
                                            <Switch
                                                id="analytics-sharing"
                                                checked={settings.privacy.analyticsSharing}
                                                onChange={(e: any) =>
                                                    updatePrivacySettings({
                                                        ...settings.privacy,
                                                        analyticsSharing: e.target.checked
                                                    })
                                                }
                                            />
                                        </div>
                                        <div className="flex items-center justify-between">
                                            <Label htmlFor="personalized-ads">Allow personalized ads</Label>
                                            <Switch
                                                id="personalized-ads"
                                                checked={settings.privacy.personalizedAds}
                                                onChange={(e: any) =>
                                                    updatePrivacySettings({
                                                        ...settings.privacy,
                                                        personalizedAds: e.target.checked
                                                    })
                                                }
                                            />
                                        </div>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Account Visibility</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <RadioGroup
                                            value={settings.privacy.visibility}
                                            onValueChange={(value) => updatePrivacySettings({
                                                ...settings.privacy,
                                                visibility: value as any
                                            })}
                                        >
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="public" id="visibility-public"/>
                                                <Label htmlFor="visibility-public">Public</Label>
                                            </div>
                                            <div className="flex items-center space-x-2">
                                                <RadioGroupItem value="private" id="visibility-private"/>
                                                <Label htmlFor="visibility-private">Private</Label>
                                            </div>
                                        </RadioGroup>
                                    </CardContent>
                                </Card>
                            </div>
                            <div className="grid gap-4 md:grid-cols-2">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Data Retention</CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        <Select
                                            value={settings.privacy.dataRetention}
                                            onValueChange={(value) => updatePrivacySettings({
                                                ...settings.privacy,
                                                dataRetention: value as any
                                            })}
                                        >
                                            <SelectTrigger id="data-retention">
                                                <SelectValue placeholder="Select Data Retention Period"/>
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="6-months">6 Months</SelectItem>
                                                <SelectItem value="1-year">1 Year</SelectItem>
                                                <SelectItem value="2-years">2 Years</SelectItem>
                                                <SelectItem value="indefinite">Indefinite</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </CardContent>
                                </Card>
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="text-lg">Third-Party Integrations</CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-2">
                                        <p className="text-sm text-muted-foreground">Connected: Google Analytics,
                                            Facebook Pixel</p>
                                        <Button variant="outline">Manage Integrations</Button>
                                    </CardContent>
                                </Card>
                            </div>
                            <div className="flex justify-between">
                                <Button variant="outline">Download Your Data</Button>
                                <Button variant="destructive">Delete My Account</Button>
                            </div>
                        </CardContent>
                        <CardFooter>
                            <Button onClick={handleSavePrivacy}>Save Privacy Settings</Button>
                        </CardFooter>
                    </Card>
                </TabsContent>
            </Tabs>
        </div>
    )
}

